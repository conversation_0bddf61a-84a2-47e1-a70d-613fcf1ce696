---
role_name_check: 1
dependency:
  name: galaxy
platforms:
  - name: ubuntu22
    cloud_image: ubuntu-2204
    vm_cpu_cores: 1
    vm_memory: 512
  - name: ubuntu24
    cloud_image: ubuntu-2404
    vm_cpu_cores: 1
    vm_memory: 512
  - name: almalinux9
    cloud_image: almalinux-9
    vm_cpu_cores: 1
    vm_memory: 512
  - name: debian12
    cloud_image: debian-12
    vm_cpu_cores: 1
    vm_memory: 512
provisioner:
  name: ansible
  config_options:
    defaults:
      callbacks_enabled: profile_tasks
      timeout: 120
  inventory:
    group_vars:
      all:
        user:
          name: foo
          comment: My test comment
  playbooks:
    create: ../../../../tests/cloud_playbooks/create-kubevirt.yml
verifier:
  name: testinfra
