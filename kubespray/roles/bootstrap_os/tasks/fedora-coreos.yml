---

- name: Check if bootstrap is needed
  raw: which python
  register: need_bootstrap
  failed_when: false
  changed_when: false
  tags:
    - facts

- name: Remove podman network cni
  raw: "podman network rm podman"
  become: true
  ignore_errors: true  # noqa ignore-errors
  when: need_bootstrap.rc != 0

- name: Clean up possible pending packages on fedora coreos
  raw: "export http_proxy={{ http_proxy | default('') }};rpm-ostree cleanup -p }}"
  become: true
  when: need_bootstrap.rc != 0

- name: Install required packages on fedora coreos
  raw: "export http_proxy={{ http_proxy | default('') }};rpm-ostree install --allow-inactive {{ fedora_coreos_packages | join(' ') }}"
  become: true
  when: need_bootstrap.rc != 0

- name: Reboot immediately for updated ostree
  raw: "nohup bash -c 'sleep 5s && shutdown -r now'"
  become: true
  ignore_errors: true  # noqa ignore-errors
  ignore_unreachable: true
  when: need_bootstrap.rc != 0

- name: Wait for the reboot to complete
  wait_for_connection:
    timeout: 240
    connect_timeout: 20
    delay: 5
    sleep: 5
  when: need_bootstrap.rc != 0
