---
## CentOS/RHEL/AlmaLinux specific variables
# Use the fastestmirror yum plugin
centos_fastestmirror_enabled: false
# Timeout (in seconds) for checking RHEL subscription status
rh_subscription_check_timeout: 180

## Flatcar Container Linux specific variables
# Disable locksmithd or leave it in its current state
coreos_locksmithd_disable: false

# Install epel repo on Centos/RHEL
epel_enabled: false

## Oracle Linux specific variables
# Install public repo on Oracle Linux
use_oracle_public_repo: true

## Ubuntu specific variables
# Disable unattended-upgrades for Linux kernel and all packages start with linux- on Ubuntu
ubuntu_kernel_unattended_upgrades_disabled: false
# Stop unattended-upgrades if it is currently running on Ubuntu
ubuntu_stop_unattended_upgrades: false

fedora_coreos_packages:
  - python
  - python3-libselinux
  - ethtool                 # required in kubeadm preflight phase for verifying the environment
  - ipset                   # required in kubeadm preflight phase for verifying the environment
  - conntrack-tools         # required by kube-proxy
  - containernetworking-plugins  # required by crio

## General
# Set the hostname to inventory_hostname
override_system_hostname: true

is_fedora_coreos: false

skip_http_proxy_on_os_packages: false

# If this is true, debug information will be displayed but
# may contain some private data, so it is recommended to set it to false
# in the production environment.
unsafe_show_logs: false
