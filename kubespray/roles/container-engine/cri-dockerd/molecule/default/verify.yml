---
- name: Test cri-dockerd
  import_playbook: ../../../molecule/test_cri.yml
  vars:
    container_manager: cri-dockerd
    cri_socket: unix:///var/run/cri-dockerd.sock
    cri_name: docker

- name: Test running a container with docker
  import_playbook: ../../../molecule/test_runtime.yml
  vars:
    container_runtime: docker
  # cri-dockerd does not support multiple runtime handler before 0.4.0
  # https://github.com/Mirantis/cri-dockerd/pull/350
  # TODO: check this when we upgrade cri-dockerd
