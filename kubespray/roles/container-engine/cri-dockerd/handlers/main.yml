---
- name: C<PERSON><PERSON>dockerd | reload systemd
  systemd_service:
    name: cri-dockerd
    daemon_reload: true
    masked: false
  listen: Restart and enable cri-dockerd

- name: Cri-dockerd | restart docker.service
  service:
    name: docker.service
    state: restarted
  listen: Restart and enable cri-dockerd

- name: Cri-dockerd | reload cri-dockerd.socket
  service:
    name: cri-dockerd.socket
    state: restarted
  listen: Restart and enable cri-dockerd

- name: Cri-dockerd | reload cri-dockerd.service
  service:
    name: cri-dockerd.service
    state: restarted
  listen: Restart and enable cri-dockerd

- name: Cri-dockerd | enable cri-dockerd service
  service:
    name: cri-dockerd.service
    enabled: true
  listen: Restart and enable cri-dockerd
