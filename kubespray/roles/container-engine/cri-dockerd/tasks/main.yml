---
- name: <PERSON><PERSON> | Download cri-dockerd binary
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.cri_dockerd) }}"

- name: Copy cri-dockerd binary from download dir
  copy:
    src: "{{ local_release_dir }}/cri-dockerd"
    dest: "{{ bin_dir }}/cri-dockerd"
    mode: "0755"
    remote_src: true
  notify:
    - Restart and enable cri-dockerd

- name: Generate cri-dockerd systemd unit files
  template:
    src: "{{ item }}.j2"
    dest: "/etc/systemd/system/{{ item }}"
    mode: "0644"
    validate: "sh -c '[ -f /usr/bin/systemd/system/factory-reset.target ] || exit 0 && systemd-analyze verify %s:{{ item }}'"
    # FIXME: check that systemd version >= 250 (factory-reset.target was introduced in that release)
    # Remove once we drop support for systemd < 250
  with_items:
    - cri-dockerd.service
    - cri-dockerd.socket
  notify:
    - Restart and enable cri-dockerd

- name: Flush handlers
  meta: flush_handlers
