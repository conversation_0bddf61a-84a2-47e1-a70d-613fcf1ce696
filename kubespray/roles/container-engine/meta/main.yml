# noqa role-name - this is a meta role that doesn't need a name
---
dependencies:
  - role: container-engine/validate-container-engine
    tags:
      - container-engine
      - validate-container-engine

  - role: container-engine/kata-containers
    when:
      - kata_containers_enabled
    tags:
      - container-engine
      - kata-containers

  - role: container-engine/gvisor
    when:
      - gvisor_enabled
      - container_manager in ['docker', 'containerd']
    tags:
      - container-engine
      - gvisor

  - role: container-engine/crun
    when:
      - crun_enabled
    tags:
      - container-engine
      - crun

  - role: container-engine/youki
    when:
      - youki_enabled
      - container_manager == 'crio'
    tags:
      - container-engine
      - youki

  - role: container-engine/cri-o
    when:
      - container_manager == 'crio'
    tags:
      - container-engine
      - crio

  - role: container-engine/containerd
    when:
      - container_manager == 'containerd'
    tags:
      - container-engine
      - containerd

  - role: container-engine/cri-dockerd
    when:
      - container_manager == 'docker'
    tags:
      - container-engine
      - docker
