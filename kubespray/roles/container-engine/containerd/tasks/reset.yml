---
- name: Containerd | Stop containerd service
  service:
    name: containerd
    daemon_reload: true
    enabled: false
    state: stopped
  tags:
    - reset_containerd

- name: Containerd | Remove configuration files
  file:
    path: "{{ item }}"
    state: absent
  loop:
    - /etc/systemd/system/containerd.service
    - "{{ containerd_systemd_dir }}"
    - "{{ containerd_cfg_dir }}"
    - "{{ containerd_storage_dir }}"
    - "{{ containerd_state_dir }}"
  tags:
    - reset_containerd
