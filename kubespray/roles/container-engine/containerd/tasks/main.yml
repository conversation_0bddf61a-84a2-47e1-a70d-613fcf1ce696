---
- name: <PERSON><PERSON><PERSON> | Download containerd
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.containerd) }}"

- name: Containerd | Unpack containerd archive
  unarchive:
    src: "{{ downloads.containerd.dest }}"
    dest: "{{ containerd_bin_dir }}"
    mode: "0755"
    remote_src: true
    extra_opts:
      - --strip-components=1
  notify: Restart containerd

- name: Containerd | Generate systemd service for containerd
  template:
    src: containerd.service.j2
    dest: /etc/systemd/system/containerd.service
    mode: "0644"
    validate: "sh -c '[ -f /usr/bin/systemd/system/factory-reset.target ] || exit 0 && systemd-analyze verify %s:containerd.service'"
    # FIXME: check that systemd version >= 250 (factory-reset.target was introduced in that release)
    # Remove once we drop support for systemd < 250
  notify: Restart containerd

- name: Containerd | Ensure containerd directories exist
  file:
    dest: "{{ item }}"
    state: directory
    mode: "0755"
    owner: root
    group: root
  with_items:
    - "{{ containerd_systemd_dir }}"
    - "{{ containerd_cfg_dir }}"
    - "{{ containerd_storage_dir }}"
    - "{{ containerd_state_dir }}"

- name: Containerd | Write containerd proxy drop-in
  template:
    src: http-proxy.conf.j2
    dest: "{{ containerd_systemd_dir }}/http-proxy.conf"
    mode: "0644"
  notify: Restart containerd
  when: http_proxy is defined or https_proxy is defined

- name: Containerd | Generate default base_runtime_spec
  register: ctr_oci_spec
  command: "{{ containerd_bin_dir }}/ctr oci spec"
  check_mode: false
  changed_when: false

- name: Containerd | Store generated default base_runtime_spec
  set_fact:
    containerd_default_base_runtime_spec: "{{ ctr_oci_spec.stdout | from_json }}"

- name: Containerd | Write base_runtime_specs
  copy:
    content: "{{ item.value }}"
    dest: "{{ containerd_cfg_dir }}/{{ item.key }}"
    owner: "root"
    mode: "0644"
  with_dict: "{{ containerd_base_runtime_specs | default({}) }}"
  notify: Restart containerd

- name: Containerd | Copy containerd config file
  template:
    src: "{{ 'config.toml.j2' if containerd_version is version('2.0.0', '>=') else 'config-v1.toml.j2' }}"
    dest: "{{ containerd_cfg_dir }}/config.toml"
    owner: "root"
    mode: "0640"
  notify: Restart containerd

- name: Containerd | Configure containerd registries
  # mirror configuration can contain sensitive information on headers configuration
  no_log: "{{ not (unsafe_show_logs | bool) }}"
  block:
    - name: Containerd | Create registry directories
      file:
        path: "{{ containerd_cfg_dir }}/certs.d/{{ item.prefix }}"
        state: directory
        mode: "0755"
      loop: "{{ containerd_registries_mirrors }}"
    - name: Containerd | Write hosts.toml file
      template:
        src: hosts.toml.j2
        dest: "{{ containerd_cfg_dir }}/certs.d/{{ item.prefix }}/hosts.toml"
        mode: "0640"
      loop: "{{ containerd_registries_mirrors }}"

# you can sometimes end up in a state where everything is installed
# but containerd was not started / enabled
- name: Containerd | Flush handlers
  meta: flush_handlers

- name: Containerd | Ensure containerd is started and enabled
  systemd_service:
    name: containerd
    daemon_reload: true
    enabled: true
    state: started
