---
containerd_storage_dir: "/var/lib/containerd"
containerd_state_dir: "/run/containerd"
containerd_systemd_dir: "/etc/systemd/system/containerd.service.d"
# The default value is not -999 here because containerd's oom_score_adj has been
# set to the -999 even if containerd_oom_score is 0.
# Ref: https://github.com/kubernetes-sigs/kubespray/pull/9275#issuecomment-1246499242
containerd_oom_score: 0

containerd_default_runtime: "runc"
containerd_snapshotter: "overlayfs"

containerd_runc_runtime:
  name: runc
  type: "io.containerd.runc.v2"
  engine: ""
  root: ""
  base_runtime_spec: cri-base.json
  options:
    SystemdCgroup: "{{ containerd_use_systemd_cgroup | ternary('true', 'false') }}"
    BinaryName: "{{ bin_dir }}/runc"

containerd_additional_runtimes: []
# Example for Kata Containers as additional runtime:
#  - name: kata
#    type: "io.containerd.kata.v2"
#    engine: ""
#    root: ""

containerd_base_runtime_spec_rlimit_nofile: 65535

containerd_default_base_runtime_spec_patch:
  process:
    rlimits:
      - type: RLIMIT_NOFILE
        hard: "{{ containerd_base_runtime_spec_rlimit_nofile }}"
        soft: "{{ containerd_base_runtime_spec_rlimit_nofile }}"

# Can help reduce disk usage
# https://github.com/containerd/containerd/discussions/6295
containerd_discard_unpacked_layers: true

containerd_base_runtime_specs:
  cri-base.json: "{{ containerd_default_base_runtime_spec | combine(containerd_default_base_runtime_spec_patch, recursive=1) }}"

containerd_grpc_max_recv_message_size: 16777216
containerd_grpc_max_send_message_size: 16777216

containerd_debug_address: ""
containerd_debug_level: "info"
containerd_debug_format: ""
containerd_debug_uid: 0
containerd_debug_gid: 0

containerd_metrics_address: ""

containerd_metrics_grpc_histogram: false

containerd_registries_mirrors:
  - prefix: docker.io
    mirrors:
      - host: https://registry-1.docker.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
#        ca: ["/etc/certs/mirror.pem"]
#        client: [["/etc/certs/client.pem", ""],["/etc/certs/client.cert", "/etc/certs/client.key"]]
#        header:
#          Authorization: "Basic XXX"
containerd_max_container_log_line_size: 16384

# If enabled it will allow non root users to use port numbers <1024
containerd_enable_unprivileged_ports: false
# If enabled it will allow non root users to use icmp sockets
containerd_enable_unprivileged_icmp: false

containerd_enable_selinux: false
containerd_disable_apparmor: false
containerd_tolerate_missing_hugetlb_controller: true
containerd_disable_hugetlb_controller: true
containerd_image_pull_progress_timeout: 5m

containerd_cfg_dir: /etc/containerd

# Extra config to be put in {{ containerd_cfg_dir }}/config.toml literally
containerd_extra_args: ''

# Configure registry auth (if applicable to secure/insecure registries)
containerd_registry_auth: []
#  - registry: ********:5000
#    username: user
#    password: pass

# Configure containerd service
containerd_limit_proc_num: "infinity"
containerd_limit_core: "infinity"
containerd_limit_open_file_num: 1048576
containerd_limit_mem_lock: "infinity"

# OS distributions that already support containerd
containerd_supported_distributions:
  - "CentOS"
  - "OracleLinux"
  - "RedHat"
  - "Ubuntu"
  - "Debian"
  - "Fedora"
  - "AlmaLinux"
  - "Rocky"
  - "Amazon"
  - "Flatcar"
  - "Flatcar Container Linux by Kinvolk"
  - "Suse"
  - "openSUSE Leap"
  - "openSUSE Tumbleweed"
  - "Kylin Linux Advanced Server"
  - "UnionTech"
  - "UniontechOS"
  - "openEuler"

# Enable container device interface
enable_cdi: false

# For containerd tracing configuration please check out the official documentation:
# https://github.com/containerd/containerd/blob/main/docs/tracing.md
containerd_tracing_enabled: false
containerd_tracing_endpoint: "[::]:4317"
containerd_tracing_protocol: "grpc"
containerd_tracing_sampling_ratio: 1.0
containerd_tracing_service_name: "containerd"
