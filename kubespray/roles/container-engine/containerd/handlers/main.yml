---
- name: <PERSON><PERSON><PERSON> | restart containerd
  systemd_service:
    name: containerd
    state: restarted
    enabled: true
    daemon-reload: true
    masked: false
  listen: Restart containerd

- name: Containerd | wait for containerd
  command: "{{ containerd_bin_dir }}/ctr images ls -q"
  register: containerd_ready
  retries: 8
  delay: 4
  until: containerd_ready.rc == 0
  listen: Restart containerd
