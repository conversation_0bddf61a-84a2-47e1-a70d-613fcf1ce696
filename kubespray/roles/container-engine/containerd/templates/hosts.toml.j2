server = "{{ item.server | default("https://" + item.prefix) }}"
{% for mirror in item.mirrors %}
[host."{{ mirror.host }}"]
  capabilities = ["{{ ([ mirror.capabilities ] | flatten ) | join('","') }}"]
  skip_verify = {{ mirror.skip_verify | default('false') | string | lower }}
  override_path = {{ mirror.override_path | default('false') | string | lower }}
{% if mirror.ca is defined %}
  ca = ["{{ ([ mirror.ca ] | flatten ) | join('","') }}"]
{% endif %}
{% if mirror.client is defined %}
  client = [{% for pair in mirror.client %}["{{ pair[0] }}", "{{ pair[1] }}"]{% if not loop.last %},{% endif %}{% endfor %}]
{% endif %}
{% if mirror.header is defined %}
  [host."{{ mirror.host }}".header]
{% for key, value in mirror.header.items() %}
    {{ key }} = ["{{ ([ value ] | flatten ) | join('","') }}"]
{% endfor %}
{% endif %}
{% endfor %}
