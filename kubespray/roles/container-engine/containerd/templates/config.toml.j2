version = 3

root = "{{ containerd_storage_dir }}"
state = "{{ containerd_state_dir }}"
oom_score = {{ containerd_oom_score }}

{% if containerd_extra_args is defined %}
{{ containerd_extra_args }}
{% endif %}

[grpc]
  max_recv_message_size = {{ containerd_grpc_max_recv_message_size }}
  max_send_message_size = {{ containerd_grpc_max_send_message_size }}

[debug]
  address = "{{ containerd_debug_address }}"
  level = "{{ containerd_debug_level }}"
  format = "{{ containerd_debug_format }}"
  uid = {{ containerd_debug_uid }}
  gid = {{ containerd_debug_gid }}

[metrics]
  address = "{{ containerd_metrics_address }}"
  grpc_histogram = {{ containerd_metrics_grpc_histogram | lower }}

[plugins]
  [plugins."io.containerd.cri.v1.runtime"]
    max_container_log_line_size = {{ containerd_max_container_log_line_size }}
    enable_unprivileged_ports = {{ containerd_enable_unprivileged_ports | lower }}
    enable_unprivileged_icmp = {{ containerd_enable_unprivileged_icmp | lower }}
    enable_selinux = {{ containerd_enable_selinux | lower }}
    disable_apparmor = {{ containerd_disable_apparmor | lower }}
    tolerate_missing_hugetlb_controller = {{ containerd_tolerate_missing_hugetlb_controller | lower }}
    disable_hugetlb_controller = {{ containerd_disable_hugetlb_controller | lower }}
{% if enable_cdi %}
    enable_cdi = true
    cdi_spec_dirs = ["/etc/cdi", "/var/run/cdi"]
{% endif %}

   [plugins."io.containerd.cri.v1.runtime".containerd]
     default_runtime_name = "{{ containerd_default_runtime }}"
     [plugins."io.containerd.cri.v1.runtime".containerd.runtimes]
{% for runtime in [containerd_runc_runtime] + containerd_additional_runtimes %}
       [plugins."io.containerd.cri.v1.runtime".containerd.runtimes.{{ runtime.name }}]
         runtime_type = "{{ runtime.type }}"
         runtime_engine = "{{ runtime.engine }}"
         runtime_root = "{{ runtime.root }}"
{% if runtime.base_runtime_spec is defined %}
         base_runtime_spec = "{{ containerd_cfg_dir }}/{{ runtime.base_runtime_spec }}"
{% endif %}

         [plugins."io.containerd.cri.v1.runtime".containerd.runtimes.{{ runtime.name }}.options]
{% for key, value in runtime.options.items() %}
{% if value | string != "true" and value | string != "false" %}
           {{ key }} = "{{ value }}"
{% else %}
           {{ key }} = {{ value }}
{% endif %}
{% endfor %}
{% endfor %}
{% if kata_containers_enabled %}
       [plugins."io.containerd.cri.v1.runtime".containerd.runtimes.kata-qemu]
         runtime_type = "io.containerd.kata-qemu.v2"
{% endif %}
{% if gvisor_enabled %}
       [plugins."io.containerd.cri.v1.runtime".containerd.runtimes.runsc]
         runtime_type = "io.containerd.runsc.v1"
{% endif %}

  [plugins."io.containerd.cri.v1.images"]
    snapshotter = "{{ containerd_snapshotter }}"
    discard_unpacked_layers = {{ containerd_discard_unpacked_layers | lower }}
    image_pull_progress_timeout = "{{ containerd_image_pull_progress_timeout }}"
  [plugins."io.containerd.cri.v1.images".pinned_images]
    sandbox = "{{ pod_infra_image_repo }}:{{ pod_infra_image_tag }}"
  [plugins."io.containerd.cri.v1.images".registry]
    config_path = "{{ containerd_cfg_dir }}/certs.d"

  [plugins."io.containerd.nri.v1.nri"]
    disable = {{ 'false' if nri_enabled else 'true' }}

{% if containerd_tracing_enabled %}
  [plugins."io.containerd.tracing.processor.v1.otlp"]
    endpoint = "{{ containerd_tracing_endpoint }}"
    protocol = "{{ containerd_tracing_protocol }}"
{% if containerd_tracing_protocol == "grpc" %}
    insecure = false
{% endif %}
  [plugins."io.containerd.internal.v1.tracing"]
    sampling_ratio = {{ containerd_tracing_sampling_ratio }}
    service_name = "{{ containerd_tracing_service_name }}"
{% endif %}
