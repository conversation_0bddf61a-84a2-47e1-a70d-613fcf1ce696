---
role_name_check: 1
platforms:
  - cloud_image: ubuntu-2404
    name: ubuntu24
    vm_cpu_cores: 1
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
  - cloud_image: debian-12
    name: debian12
    vm_cpu_cores: 1
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
  - cloud_image: almalinux-9
    name: almalinux9
    vm_cpu_cores: 1
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
provisioner:
  name: ansible
  env:
    ANSIBLE_ROLES_PATH: ../../../../
  config_options:
    defaults:
      callbacks_enabled: profile_tasks
      timeout: 120
  playbooks:
    create: ../../../../../tests/cloud_playbooks/create-kubevirt.yml
    prepare: ../../../molecule/prepare.yml
verifier:
  name: ansible
