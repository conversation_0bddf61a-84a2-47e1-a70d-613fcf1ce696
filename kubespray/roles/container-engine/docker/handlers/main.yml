---
- name: <PERSON><PERSON> | reload systemd
  systemd_service:
    name: docker
    daemon_reload: true
    masked: false
  listen: Restart docker

- name: Dock<PERSON> | reload docker.socket
  service:
    name: docker.socket
    state: restarted
  when: ansible_os_family in ['Flatcar', 'Flatcar Container Linux by Kinvolk'] or is_fedora_coreos
  listen: Restart docker


- name: Docker | reload docker
  service:
    name: docker
    state: restarted
  listen: Restart docker


- name: Docker | wait for docker
  command: "{{ docker_bin_dir }}/docker images"
  register: docker_ready
  retries: 20
  delay: 1
  until: docker_ready.rc == 0
  listen: Restart docker
