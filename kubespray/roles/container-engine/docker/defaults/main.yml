---
docker_version: '28.0'
docker_cli_version: "{{ docker_version }}"

docker_package_info:
  pkgs:

# Path where to store repo key
# docker_repo_key_keyring: /etc/apt/trusted.gpg.d/docker.gpg

docker_repo_key_info:
  repo_keys:

docker_repo_info:
  repos:

docker_cgroup_driver: systemd

docker_bin_dir: "/usr/bin"

# flag to enable/disable docker cleanup
docker_orphan_clean_up: false

# old docker package names to be removed
docker_remove_packages_yum:
  - docker
  - docker-common
  - docker-engine
  - docker-selinux.noarch
  - docker-client
  - docker-client-latest
  - docker-latest
  - docker-latest-logrotate
  - docker-logrotate
  - docker-engine-selinux.noarch

# remove podman to avoid containerd.io confliction
podman_remove_packages_yum:
  - podman

docker_remove_packages_apt:
  - docker
  - docker-engine
  - docker.io

# Docker specific repos should be part of the docker role not containerd-common anymore
# Optional values for containerd apt repo
containerd_package_info:
  pkgs:

# Fedora docker-ce repo
docker_fedora_repo_base_url: 'https://download.docker.com/linux/fedora/{{ ansible_distribution_major_version }}/$basearch/stable'
docker_fedora_repo_gpgkey: 'https://download.docker.com/linux/fedora/gpg'

# CentOS/RedHat docker-ce repo
docker_rh_repo_base_url: 'https://download.docker.com/linux/rhel/{{ ansible_distribution_major_version }}/$basearch/stable'
docker_rh_repo_gpgkey: 'https://download.docker.com/linux/rhel/gpg'

# Ubuntu docker-ce repo
docker_ubuntu_repo_base_url: "https://download.docker.com/linux/ubuntu"
docker_ubuntu_repo_gpgkey: 'https://download.docker.com/linux/ubuntu/gpg'
docker_ubuntu_repo_repokey: '9DC858229FC7DD38854AE2D88D81803C0EBFCD88'

# Debian docker-ce repo
docker_debian_repo_base_url: "https://download.docker.com/linux/debian"
docker_debian_repo_gpgkey: 'https://download.docker.com/linux/debian/gpg'
docker_debian_repo_repokey: '9DC858229FC7DD38854AE2D88D81803C0EBFCD88'
