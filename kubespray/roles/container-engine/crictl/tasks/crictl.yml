---
- name: <PERSON><PERSON><PERSON> | Download crictl
  include_tasks: "../../../download/tasks/download_file.yml"
  vars:
    download: "{{ download_defaults | combine(downloads.crictl) }}"

- name: Install crictl config
  template:
    src: crictl.yaml.j2
    dest: /etc/crictl.yaml
    owner: root
    mode: "0644"

- name: Copy crictl binary from download dir
  copy:
    src: "{{ local_release_dir }}/crictl"
    dest: "{{ bin_dir }}/crictl"
    mode: "0755"
    remote_src: true
  notify:
    - Get crictl completion
    - Install crictl completion
