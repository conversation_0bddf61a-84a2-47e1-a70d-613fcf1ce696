---
role_name_check: 1
platforms:
  - name: ubuntu22
    cloud_image: ubuntu-2204
    vm_cpu_cores: 2
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
  - name: almalinux9
    cloud_image: almalinux-9
    vm_cpu_cores: 2
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
  - name: fedora
    cloud_image: fedora-39
    vm_cpu_cores: 2
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
  - name: debian12
    cloud_image: debian-12
    vm_cpu_cores: 2
    vm_memory: 1024
    node_groups:
      - kube_control_plane
      - kube_node
      - k8s_cluster
provisioner:
  name: ansible
  env:
    ANSIBLE_ROLES_PATH: ../../../../
  config_options:
    defaults:
      callbacks_enabled: profile_tasks
      timeout: 120
  playbooks:
    create: ../../../../../tests/cloud_playbooks/create-kubevirt.yml
    prepare: ../../../molecule/prepare.yml
verifier:
  name: ansible
