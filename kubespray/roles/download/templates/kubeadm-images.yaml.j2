apiVersion: kubeadm.k8s.io/{{ kubeadm_config_api_version }}
kind: InitConfiguration
nodeRegistration:
  criSocket: {{ cri_socket }}
---
apiVersion: kubeadm.k8s.io/{{ kubeadm_config_api_version }}
kind: ClusterConfiguration
imageRepository: {{ kube_image_repo }}
kubernetesVersion: v{{ kube_version }}
etcd:
{% if etcd_deployment_type == "kubeadm" %}
  local:
    imageRepository: "{{ etcd_image_repo | regex_replace("/etcd$","") }}"
    imageTag: "{{ etcd_image_tag }}"
{% else %}
  external:
      endpoints:
{% for endpoint in etcd_access_addresses.split(',') %}
      - {{ endpoint }}
{% endfor %}
{% endif %}
dns:
  imageRepository: {{ coredns_image_repo | regex_replace('/coredns(?!/coredns).*$', '') }}
  imageTag: {{ coredns_image_tag }}
