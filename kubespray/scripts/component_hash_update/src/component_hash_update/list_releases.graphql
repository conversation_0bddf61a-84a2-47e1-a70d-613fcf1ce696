query($with_releases: [ID!]!, $with_tags: [ID!]!) {
  with_releases: nodes(ids: $with_releases) {

    ... on Repository {
      releases(first: 100) {
        nodes {
          tagName
          isPrerelease
        }
      }
    }
  }

  with_tags: nodes(ids: $with_tags) {

    ... on Repository {
      refs(refPrefix: "refs/tags/", last: 25) {
        nodes {
          name
        }
      }
    }
  }
}
