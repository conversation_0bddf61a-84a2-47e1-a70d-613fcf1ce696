[build-system]
requires = ["setuptools >= 61.0",
            "setuptools_scm >= 8.0",
]
build-backend = "setuptools.build_meta"

[project]
name = "kubespray_component_hash_update"
version = "1.0.0"
dependencies = [
  "more_itertools",
  "ruamel.yaml",
  "requests",
  "packaging",
]

requires-python = ">= 3.10"

authors = [
   { name = "<PERSON> Rodrig<PERSON>", email = "<EMAIL>" },
   { name = "<PERSON>" },
   { name = "<PERSON>autier", email = "<EMAIL>" },
]
maintainers = [
   { name = "The Kubespray maintainers" },
]

description = "Download or compute hashes for new versions of components deployed by Kubespray"

classifiers = [
    "License :: OSI Approved :: Apache-2.0",
]

[project.scripts]
update-hashes = "component_hash_update.download:main"
