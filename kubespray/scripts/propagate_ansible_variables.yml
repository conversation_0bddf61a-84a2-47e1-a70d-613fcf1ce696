#!/usr/bin/env ansible-playbook
---
- name: Update README.md versions
  hosts: localhost
  connection: local
  gather_facts: false
  vars:
    fallback_ip: 'bypass tasks in kubespray_defaults'
  roles:
  - kubespray_defaults
  tasks:
  - name: Include versions not in kubespray_defaults
    include_vars: "{{ item }}"
    loop:
    - ../roles/container-engine/docker/defaults/main.yml
    - ../roles/kubernetes/node/defaults/main.yml
    - ../roles/kubernetes-apps/argocd/defaults/main.yml
  - name: Render versions in README.md
    blockinfile:
      marker: '<!-- {mark} ANSIBLE MANAGED BLOCK -->'
      block: "\n{{ lookup('ansible.builtin.template', 'readme_versions.md.j2') }}\n\n"
      path: ../README.md
  - name: Render Dockerfiles
    template:
      src: "{{ item }}.j2"
      dest: "../{{ item }}"
      mode: "0644"
    loop:
    - 'pipeline.Dockerfile'
    - 'Dockerfile'
