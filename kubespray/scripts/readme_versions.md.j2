- Core
  - [kubernetes](https://github.com/kubernetes/kubernetes) {{ kube_version }}
  - [etcd](https://github.com/etcd-io/etcd) {{ etcd_version }}
  - [docker](https://www.docker.com/) {{ docker_version }}
  - [containerd](https://containerd.io/) {{ containerd_version }}
  - [cri-o](http://cri-o.io/) {{ crio_version }} (experimental: see [CRI-O Note](docs/CRI/cri-o.md). Only on fedora, ubuntu and centos based OS)
- Network Plugin
  - [cni-plugins](https://github.com/containernetworking/plugins) {{ cni_version }}
  - [calico](https://github.com/projectcalico/calico) {{ calico_version }}
  - [cilium](https://github.com/cilium/cilium) {{ cilium_version }}
  - [flannel](https://github.com/flannel-io/flannel) {{ flannel_version }}
  - [kube-ovn](https://github.com/alauda/kube-ovn) {{ kube_ovn_version }}
  - [kube-router](https://github.com/cloudnativelabs/kube-router) {{ kube_router_version }}
  - [multus](https://github.com/k8snetworkplumbingwg/multus-cni) {{ multus_version }}
  - [kube-vip](https://github.com/kube-vip/kube-vip) {{ kube_vip_version }}
- Application
  - [cert-manager](https://github.com/jetstack/cert-manager) {{ cert_manager_version }}
  - [coredns](https://github.com/coredns/coredns) {{ coredns_version }}
  - [ingress-nginx](https://github.com/kubernetes/ingress-nginx) {{ ingress_nginx_version }}
  - [argocd](https://argoproj.github.io/) {{ argocd_version }}
  - [helm](https://helm.sh/) {{ helm_version }}
  - [metallb](https://metallb.universe.tf/) {{ metallb_version }}
  - [registry](https://github.com/distribution/distribution) {{ registry_version }}
- Storage Plugin
  - [aws-ebs-csi-plugin](https://github.com/kubernetes-sigs/aws-ebs-csi-driver) {{ aws_ebs_csi_plugin_version }}
  - [azure-csi-plugin](https://github.com/kubernetes-sigs/azuredisk-csi-driver) {{ azure_csi_plugin_version }}
  - [cinder-csi-plugin](https://github.com/kubernetes/cloud-provider-openstack/blob/master/docs/cinder-csi-plugin/using-cinder-csi-plugin.md) {{ cinder_csi_plugin_version }}
  - [gcp-pd-csi-plugin](https://github.com/kubernetes-sigs/gcp-compute-persistent-disk-csi-driver) {{ gcp_pd_csi_plugin_version }}
  - [local-path-provisioner](https://github.com/rancher/local-path-provisioner) {{ local_path_provisioner_version }}
  - [local-volume-provisioner](https://github.com/kubernetes-sigs/sig-storage-local-static-provisioner) {{ local_volume_provisioner_version }}
  - [node-feature-discovery](https://github.com/kubernetes-sigs/node-feature-discovery) {{ node_feature_discovery_version }}
