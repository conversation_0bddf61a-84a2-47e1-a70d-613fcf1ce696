# CI test coverage

To generate this Matrix run `./tests/scripts/md-table/main.py`

{%- for container_engine in container_engines %}

## {{ container_engine }}

| OS / CNI |{% for cni in network_plugins %} {{ cni }} |{% endfor %}
|---|{% for cni in network_plugins %} --- |{% endfor %}
{%- for os in operating_systems %}
{{ os }} | {% for cni in network_plugins %} {{ ':white_check_mark:' if exists(container_engine, cni, os) else ':x:' }} |{% endfor %}
{%- endfor %}

{%- endfor %}
