---
# Kubespray settings for tests
deploy_netchecker: true
dns_min_replicas: 1
unsafe_show_logs: true

bin_dir: "{{ '/opt/bin' if ansible_os_family == 'Flatcar' else '/usr/local/bin' }}"

# Registry mirrors settings
docker_registry_mirrors:
  - "https://mirror.gcr.io"

containerd_registries_mirrors:
  - prefix: docker.io
    mirrors:
      - host: https://mirror.gcr.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
      - host: https://registry-1.docker.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
crio_registries:
  - prefix: docker.io
    insecure: false
    blocked: false
    unqualified: false
    location: registry-1.docker.io
    mirrors:
      - location: mirror.gcr.io
        insecure: false

netcheck_agent_image_repo: "{{ quay_image_repo }}/kubespray/k8s-netchecker-agent"
netcheck_server_image_repo: "{{ quay_image_repo }}/kubespray/k8s-netchecker-server"

nginx_image_repo: "{{ quay_image_repo }}/kubespray/nginx"

flannel_image_repo: "{{ quay_image_repo }}/kubespray/flannel"
flannel_init_image_repo: "{{ quay_image_repo }}/kubespray/flannel-cni-plugin"
