---
- name: Download sonobuoy
  get_url:
    url: "https://github.com/vmware-tanzu/sonobuoy/releases/download/v{{ sonobuoy_version }}/sonobuoy_{{ sonobuoy_version }}_linux_{{ sonobuoy_arch }}.tar.gz"
    dest: /tmp/sonobuoy.tar.gz
    mode: "0644"

- name: Extract sonobuoy
  unarchive:
    src: /tmp/sonobuoy.tar.gz
    dest: /usr/local/bin/
    copy: false

- name: Run sonobuoy
  command: "{{ sonobuoy_path }} run --mode {{ sonobuoy_mode }} --e2e-parallel {{ sonobuoy_parallel }} --wait"

- name: Run sonobuoy retrieve
  command: "{{ sonobuoy_path }} retrieve"
  register: sonobuoy_retrieve

- name: Run inspect results
  command: "{{ sonobuoy_path }} results {{ sonobuoy_retrieve.stdout }} --plugin e2e --mode report"
