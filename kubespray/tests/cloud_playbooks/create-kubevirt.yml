---
- name: Provision Packet VMs
  hosts: localhost
  gather_facts: false
  become: true
  tasks:
  - name: Create Kubevirt VMs
    import_role:
      name: packet-ci
  - name: Update inventory for Molecule
    meta: refresh_inventory

- name: Wait until SSH is available
  hosts: all
  become: false
  gather_facts: false

  tasks:
  - name: Wait until SSH is available
    wait_for:
      host: "{{ ansible_host }}"
      port: 22
      timeout: 240
    delegate_to: localhost
