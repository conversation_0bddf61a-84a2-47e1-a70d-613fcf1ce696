---
# This is a list of nodes with groups for each scenario/cluster layouts
scenarios:
  separate:
    - node_groups: ['kube_control_plane']
    - node_groups: ['kube_node']
    - node_groups: ['etcd']
  ha:
    - node_groups: ['kube_control_plane', 'etcd']
    - node_groups: ['kube_control_plane', 'etcd']
    - node_groups: ['kube_node', 'etcd']
  default:
    - node_groups: ['kube_control_plane', 'etcd']
    - node_groups: ['kube_node']
  all-in-one:
    - node_groups: ['kube_control_plane', 'etcd', 'kube_node']
  ha-recover:
    - node_groups: ['kube_control_plane', 'etcd']
    - node_groups: ['kube_control_plane', 'etcd', 'broken_kube_control_plane', 'broken_etcd']
    - node_groups: ['kube_node', 'etcd']
  ha-recover-noquorum:
    - node_groups: ['kube_control_plane', 'etcd', 'broken_kube_control_plane', 'broken_etcd']
    - node_groups: ['kube_control_plane', 'etcd', 'broken_kube_control_plane', 'broken_etcd']
    - node_groups: ['kube_node', 'etcd']
  node-etcd-client:
    - node_groups: ['kube_node', 'kube_control_plane', 'etcd']
    - node_groups: ['kube_node', 'etcd']
    - node_groups: ['kube_node', 'etcd']
    - node_groups: ['kube_node']

# Get pod metadata / CI vars from environment

ci_job_id: "{{ lookup('ansible.builtin.env', 'CI_JOB_ID', default=undefined) }}"
pod_name: "{{ lookup('ansible.builtin.env', 'POD_NAME', default=undefined) }}"
pod_uid: "{{ lookup('ansible.builtin.env', 'POD_UID', default=undefined) }}"
pod_namespace: "{{ lookup('ansible.builtin.env', 'POD_NAMESPACE', default=undefined) }}"

cloudinit_config: |
 #cloud-config
  users:
    - name: {{ lookup('env', 'ANSIBLE_REMOTE_USER') }}
      sudo: ALL=(ALL) NOPASSWD:ALL
      shell: /bin/bash
      lock_passwd: False
      ssh_authorized_keys:
        - {{ ssh_key.public_key }}

ignition_config:
  ignition:
    version: "3.2.0"
  passwd:
    users:
      - name: "{{ lookup('env', 'ANSIBLE_REMOTE_USER') }}"
        groups:
          - sudo
          - wheel
        sshAuthorizedKeys:
          - "{{ ssh_key.public_key }}"
