---
apiVersion: kubevirt.io/v1
kind: VirtualMachineInstance
metadata:
  generateName: {{ cloud_image }}-
  namespace: {{ pod_namespace }}
  annotations:
    kubespray.com/ci.template-path: "tests/cloud_playbooks/roles/packet-ci/templates/vm.yml.j2"
    ansible_groups: "{{ node_groups | join(',') }}"
    inventory_name: "{{ name | d(cloud_image ~ '-' ~ index) }}"
    # This does not use a dns prefix because dots are hard to escape with map(attribute=) in Jinja
  labels:
    kubevirt.io/os: {{ cloud_image }}
    kubevirt.io/size: small
    ci_job_id: "{{ ci_job_id }}"
    ci_job_name: "{{ lookup('ansible.builtin.env', 'CI_JOB_NAME_SLUG') }}"
  # leverage the Kubernetes GC for resources cleanup
  ownerReferences:
  - apiVersion: v1
    kind: Pod
    name: "{{ pod_name }}"
    uid: "{{ pod_uid }}"
spec:
  domain:
    devices:
      blockMultiQueue: true
      disks:
        - disk:
            bus: virtio
          name: containervolume
          cache: writethrough
        - disk:
            bus: virtio
          name: cloudinitvolume
      interfaces:
      - name: default
        bridge: {}
    cpu:
        cores: {{ vm_cpu_cores }}
        sockets: {{ vm_cpu_sockets }}
        threads: {{ vm_cpu_threads }}
    resources:
      requests:
        memory: "{{ vm_memory * memory_allocation_ratio }}Mi"
        cpu: {{ vm_cpu_cores * cpu_allocation_ratio }}
      limits:
        memory: "{{ vm_memory }}Mi"
        cpu: {{ vm_cpu_cores }}
  networks:
  - name: default
    pod: {}
  terminationGracePeriodSeconds: 0
  volumes:
    - name: containervolume
      containerDisk:
        image: quay.io/kubespray/vm-{{ cloud_image }}
    - name: cloudinitvolume
      cloudInit{{ 'ConfigDrive' if cloud_image.startswith('flatcar') else 'NoCloud' }}:
        userDataBase64: '{{ ((ignition_config | to_json) if cloud_image.startswith('flatcar') else cloudinit_config) | b64encode }}'
