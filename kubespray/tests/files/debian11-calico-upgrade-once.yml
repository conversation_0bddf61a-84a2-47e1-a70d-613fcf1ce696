---
# Instance settings
cloud_image: debian-11

# Kubespray settings
download_run_once: true

# Pin disabling ipip mode to ensure proper upgrade
ipip: false
calico_pool_blocksize: 26
calico_vxlan_mode: Always
calico_network_backend: bird

# Needed to bypass deprecation check
ignore_assert_errors: true

# Use static containerd binary for older distributions like Debian 11.
containerd_static_binary: true
