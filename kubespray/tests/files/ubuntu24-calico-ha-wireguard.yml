---
# Instance settings
cloud_image: ubuntu-2404
mode: ha

# Kubespray settings
calico_wireguard_enabled: true
auto_renew_certificates: true

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=focal&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: iptables
# KVM kernel used by packet instances is missing the dummy.ko kernel module so it cannot enable nodelocaldns
enable_nodelocaldns: false
