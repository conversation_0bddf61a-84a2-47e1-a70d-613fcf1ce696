---
# Instance settings
cloud_image: ubuntu-2404

# use the kubeadm etcd setting to test the upgrade
etcd_deployment_type: kubeadm

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=focal&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: iptables
enable_nodelocaldns: false

# Remove anonymous access to cluster
remove_anonymous_access: true
