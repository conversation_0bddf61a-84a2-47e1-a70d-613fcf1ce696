---
# Instance settings
cloud_image: ubuntu-2404
mode: node-etcd-client
vm_memory: 1800

# Kubespray settings
auto_renew_certificates: true

# Currently ipvs not available on KVM: https://packages.ubuntu.com/search?suite=noble&arch=amd64&mode=exactfilename&searchon=contents&keywords=ip_vs_sh.ko
kube_proxy_mode: nftables
enable_nodelocaldns: false

containerd_registries:
  "docker.io": "https://mirror.gcr.io"

containerd_registries_mirrors:
  - prefix: docker.io
    mirrors:
      - host: https://mirror.gcr.io
        capabilities: ["pull", "resolve"]
        skip_verify: false
  - prefix: ************:5000
    mirrors:
      - host: http://************:5000
        capabilities: ["pull", "resolve", "push"]
        skip_verify: true

calico_datastore: "etcd"

# Test kubeadm patches
kubeadm_patches:
  - target: kube-apiserver
    patch:
      metadata:
        annotations:
          example.com/test: "true"
        labels:
          example.com/prod_level: "prep"
  - target: kube-controller-manager
    patch:
      metadata:
        annotations:
          example.com/test: "false"
        labels:
          example.com/prod_level: "prep"

# ntp settings
ntp_enabled: true
ntp_package: ntpsec
