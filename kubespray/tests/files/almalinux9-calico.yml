---
# Instance settings
cloud_image: almalinux-9
vm_memory: 3072

# Kubespray settings
metrics_server_enabled: true
dashboard_namespace: "kube-dashboard"
dashboard_enabled: true
loadbalancer_apiserver_type: haproxy
local_path_provisioner_enabled: true

kube_proxy_mode: nftables

# NTP mangement
ntp_enabled: true
ntp_package: chrony
ntp_timezone: Etc/UTC
ntp_manage_config: true
ntp_tinker_panic: true
ntp_force_sync_immediately: true

# Scheduler plugins
scheduler_plugins_enabled: true
