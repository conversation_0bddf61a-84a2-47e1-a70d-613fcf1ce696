#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kubespray PR Analysis Script
分析Kubespray近半年的PR变更
"""

import json
import re
from datetime import datetime
from collections import defaultdict, Counter
from typing import Dict, List, Any

def extract_kind_labels(labels: List[Dict]) -> List[str]:
    """提取kind标签"""
    kind_labels = []
    for label in labels:
        if label['name'].startswith('kind/'):
            kind_labels.append(label['name'])
    return kind_labels

def generate_chinese_summary(title: str, body: str) -> str:
    """根据PR标题和描述生成中文摘要（不超过50字符）"""

    # 关键词映射
    keyword_mapping = {
        'fix': '修复',
        'bug': '错误',
        'feature': '新功能',
        'cleanup': '清理',
        'documentation': '文档',
        'update': '更新',
        'upgrade': '升级',
        'add': '添加',
        'remove': '移除',
        'support': '支持',
        'improve': '改进',
        'refactor': '重构',
        'test': '测试',
        'ci': 'CI',
        'security': '安全',
        'performance': '性能',
        'config': '配置',
        'version': '版本',
        'dependency': '依赖',
        'containerd': 'containerd',
        'kubernetes': 'K8s',
        'k8s': 'K8s',
        'calico': 'Calico',
        'flannel': 'Flannel',
        'etcd': 'etcd',
        'ansible': 'Ansible',
        'terraform': 'Terraform',
        'ubuntu': 'Ubuntu',
        'centos': 'CentOS',
        'debian': 'Debian',
        'fedora': 'Fedora',
        'rocky': 'Rocky',
        'alma': 'Alma',
        'gateway': '网关',
        'api': 'API',
        'cni': 'CNI',
        'csi': 'CSI',
        'ingress': 'Ingress',
        'loadbalancer': '负载均衡',
        'metallb': 'MetalLB',
        'nginx': 'Nginx',
        'haproxy': 'HAProxy',
        'cert': '证书',
        'tls': 'TLS',
        'ssl': 'SSL',
        'rbac': 'RBAC',
        'serviceaccount': '服务账户',
        'namespace': '命名空间',
        'pod': 'Pod',
        'deployment': '部署',
        'service': '服务',
        'configmap': '配置映射',
        'secret': '密钥',
        'pv': '持久卷',
        'pvc': '持久卷声明',
        'storage': '存储',
        'network': '网络',
        'dns': 'DNS',
        'coredns': 'CoreDNS',
        'proxy': '代理',
        'firewall': '防火墙',
        'iptables': 'iptables',
        'selinux': 'SELinux',
        'systemd': 'systemd',
        'docker': 'Docker',
        'crio': 'CRI-O',
        'runc': 'runc',
        'cgroup': 'cgroup',
        'kernel': '内核',
        'mount': '挂载',
        'volume': '卷',
        'backup': '备份',
        'restore': '恢复',
        'monitoring': '监控',
        'logging': '日志',
        'metrics': '指标',
        'prometheus': 'Prometheus',
        'grafana': 'Grafana',
        'alertmanager': 'Alertmanager',
        'dashboard': '仪表板',
        'helm': 'Helm',
        'operator': '操作器',
        'crd': 'CRD',
        'webhook': 'Webhook',
        'admission': '准入',
        'controller': '控制器',
        'scheduler': '调度器',
        'kubelet': 'kubelet',
        'kube-proxy': 'kube-proxy',
        'kube-apiserver': 'kube-apiserver',
        'kube-controller-manager': 'kube-controller-manager',
        'kube-scheduler': 'kube-scheduler',
        'cloud': '云',
        'aws': 'AWS',
        'azure': 'Azure',
        'gcp': 'GCP',
        'openstack': 'OpenStack',
        'vsphere': 'vSphere',
        'baremetal': '裸机',
        'vagrant': 'Vagrant',
        'virtualbox': 'VirtualBox',
        'libvirt': 'libvirt',
        'qemu': 'QEMU',
        'kvm': 'KVM',
        'hyper-v': 'Hyper-V',
        'vmware': 'VMware',
        'upcloud': 'UpCloud',
        'hetzner': 'Hetzner',
        'scaleway': 'Scaleway',
        'packet': 'Packet',
        'equinix': 'Equinix',
        'digitalocean': 'DigitalOcean',
        'linode': 'Linode',
        'vultr': 'Vultr',
        'oracle': 'Oracle',
        'alibaba': '阿里云',
        'tencent': '腾讯云',
        'huawei': '华为云',
        'baidu': '百度云'
    }

    # 合并标题和描述进行分析
    text = (title + ' ' + (body or '')).lower()

    # 提取关键信息
    summary_parts = []

    # 检查操作类型
    if 'fix' in text or 'bug' in text:
        summary_parts.append('修复')
    elif 'add' in text or 'support' in text:
        summary_parts.append('添加')
    elif 'update' in text or 'upgrade' in text or 'bump' in text:
        summary_parts.append('更新')
    elif 'remove' in text or 'delete' in text:
        summary_parts.append('移除')
    elif 'refactor' in text or 'cleanup' in text:
        summary_parts.append('重构')
    elif 'improve' in text or 'enhance' in text:
        summary_parts.append('改进')
    elif 'doc' in text:
        summary_parts.append('文档')
    elif 'test' in text:
        summary_parts.append('测试')
    elif 'ci' in text:
        summary_parts.append('CI')
    else:
        summary_parts.append('更改')

    # 检查组件
    components = []
    for keyword, chinese in keyword_mapping.items():
        if keyword in text:
            components.append(chinese)

    # 去重并限制组件数量
    components = list(dict.fromkeys(components))[:3]

    if components:
        summary_parts.extend(components)

    # 生成摘要
    summary = ''.join(summary_parts[:4])  # 限制长度

    # 确保不超过50个字符
    if len(summary) > 50:
        summary = summary[:47] + '...'

    return summary if summary else '代码更改'

def analyze_prs(all_prs: List[Dict]) -> Dict[str, Any]:
    """分析所有PR数据"""

    # 按kind标签分类
    kind_stats = defaultdict(list)

    # 按月份统计
    monthly_stats = defaultdict(int)

    # K8s版本更新相关
    k8s_version_prs = []

    # 操作系统支持相关
    os_support_prs = []

    # 处理每个PR
    for pr in all_prs:
        # 提取合并日期
        merged_at = pr['pull_request']['merged_at']
        if merged_at:
            merge_date = datetime.fromisoformat(merged_at.replace('Z', '+00:00'))
            month_key = merge_date.strftime('%Y-%m')
            monthly_stats[month_key] += 1

        # 提取kind标签
        kind_labels = extract_kind_labels(pr['labels'])

        # 生成中文摘要
        chinese_summary = generate_chinese_summary(pr['title'], pr['body'])

        # 创建PR信息
        pr_info = {
            'number': pr['number'],
            'title': pr['title'],
            'body': pr['body'],
            'html_url': pr['html_url'],
            'merged_at': merged_at,
            'kind_labels': kind_labels,
            'chinese_summary': chinese_summary,
            'labels': [label['name'] for label in pr['labels']]
        }

        # 按kind分类
        if kind_labels:
            for kind in kind_labels:
                kind_stats[kind].append(pr_info)
        else:
            kind_stats['kind/other'].append(pr_info)

        # 检查K8s版本更新
        title_body = (pr['title'] + ' ' + (pr['body'] or '')).lower()
        if any(keyword in title_body for keyword in ['kubernetes', 'k8s', 'kubeadm', 'kubelet', 'kube-proxy', 'kube-apiserver']):
            if any(keyword in title_body for keyword in ['version', 'update', 'upgrade', 'bump']):
                k8s_version_prs.append(pr_info)

        # 检查操作系统支持
        if any(keyword in title_body for keyword in ['ubuntu', 'centos', 'debian', 'fedora', 'rocky', 'alma', 'rhel', 'suse', 'opensuse']):
            if any(keyword in title_body for keyword in ['support', 'add', 'remove', 'deprecate', 'drop']):
                os_support_prs.append(pr_info)

    return {
        'kind_stats': dict(kind_stats),
        'monthly_stats': dict(monthly_stats),
        'k8s_version_prs': k8s_version_prs,
        'os_support_prs': os_support_prs,
        'total_prs': len(all_prs)
    }

def generate_report(analysis_result: Dict[str, Any]) -> str:
    """生成中文报告"""

    report = []
    report.append("# Kubespray 近半年 PR 变更分析报告")
    report.append(f"**分析时间范围**: 2025年2月1日 - 2025年7月31日")
    report.append(f"**总计合并PR数量**: {analysis_result['total_prs']} 个")
    report.append("")

    # 1. PR分类统计
    report.append("## 1. PR 类型分类统计")
    report.append("")

    kind_stats = analysis_result['kind_stats']

    # 按数量排序
    sorted_kinds = sorted(kind_stats.items(), key=lambda x: len(x[1]), reverse=True)

    for kind, prs in sorted_kinds:
        kind_name = kind.replace('kind/', '')
        kind_chinese = {
            'feature': '新功能',
            'bug': '错误修复',
            'cleanup': '代码清理',
            'documentation': '文档更新',
            'failing-test': '测试修复',
            'api-change': 'API变更',
            'deprecation': '功能弃用',
            'design': '设计变更',
            'flake': '不稳定测试',
            'container-managers': '容器管理器',
            'other': '其他'
        }.get(kind_name, kind_name)

        report.append(f"### {kind_chinese} ({kind}) - {len(prs)} 个")
        report.append("")

        # 显示前10个PR
        for pr in prs[:10]:
            pr_link = f"[#{pr['number']}]({pr['html_url']})"
            report.append(f"- {pr_link} {pr['chinese_summary']}")

        if len(prs) > 10:
            report.append(f"- ... 还有 {len(prs) - 10} 个PR")

        report.append("")

    return '\n'.join(report)

    # 2. 月度PR统计
    report.append("## 2. 月度 PR 数量统计")
    report.append("")

    monthly_stats = analysis_result['monthly_stats']
    sorted_months = sorted(monthly_stats.items())

    report.append("| 月份 | PR数量 | 环比变化 |")
    report.append("|------|--------|----------|")

    prev_count = 0
    for month, count in sorted_months:
        if prev_count > 0:
            change = count - prev_count
            change_str = f"+{change}" if change > 0 else str(change)
            change_percent = f"({change/prev_count*100:+.1f}%)" if prev_count > 0 else ""
        else:
            change_str = "-"
            change_percent = ""

        month_chinese = {
            '2025-02': '2月',
            '2025-03': '3月',
            '2025-04': '4月',
            '2025-05': '5月',
            '2025-06': '6月',
            '2025-07': '7月'
        }.get(month, month)

        report.append(f"| {month_chinese} | {count} | {change_str} {change_percent} |")
        prev_count = count

    report.append("")

    # 3. K8s版本更新
    report.append("## 3. Kubernetes 集群版本更新")
    report.append("")

    k8s_prs = analysis_result['k8s_version_prs']
    if k8s_prs:
        report.append(f"本期间共有 {len(k8s_prs)} 个与K8s版本更新相关的PR：")
        report.append("")

        for pr in k8s_prs:
            pr_link = f"[#{pr['number']}]({pr['html_url']})"
            report.append(f"- {pr_link} {pr['chinese_summary']}")

        report.append("")
    else:
        report.append("本期间没有发现明显的K8s版本更新相关PR。")
        report.append("")

    # 4. 操作系统支持变化
    report.append("## 4. 操作系统支持变化")
    report.append("")

    os_prs = analysis_result['os_support_prs']
    if os_prs:
        report.append(f"本期间共有 {len(os_prs)} 个与操作系统支持相关的PR：")
        report.append("")

        for pr in os_prs:
            pr_link = f"[#{pr['number']}]({pr['html_url']})"
            report.append(f"- {pr_link} {pr['chinese_summary']}")

        report.append("")
    else:
        report.append("本期间没有发现明显的操作系统支持变化相关PR。")
        report.append("")

    # 5. 社区趋势分析
    report.append("## 5. Kubespray 社区趋势分析")
    report.append("")

    # 分析趋势
    total_prs = analysis_result['total_prs']
    feature_count = len(kind_stats.get('kind/feature', []))
    bug_count = len(kind_stats.get('kind/bug', []))
    cleanup_count = len(kind_stats.get('kind/cleanup', []))
    doc_count = len(kind_stats.get('kind/documentation', []))

    report.append("### 主要趋势观察：")
    report.append("")

    report.append(f"1. **开发活跃度**: 半年内合并了 {total_prs} 个PR，平均每月约 {total_prs/6:.1f} 个")
    report.append("")

    if feature_count > 0:
        report.append(f"2. **新功能开发**: 新功能相关PR占比 {feature_count/total_prs*100:.1f}% ({feature_count}个)，显示社区持续创新")

    if bug_count > 0:
        report.append(f"3. **质量改进**: 错误修复PR占比 {bug_count/total_prs*100:.1f}% ({bug_count}个)，体现对稳定性的重视")

    if cleanup_count > 0:
        report.append(f"4. **代码维护**: 代码清理PR占比 {cleanup_count/total_prs*100:.1f}% ({cleanup_count}个)，显示良好的代码维护习惯")

    if doc_count > 0:
        report.append(f"5. **文档完善**: 文档更新PR占比 {doc_count/total_prs*100:.1f}% ({doc_count}个)，重视用户体验")

    report.append("")

    # 月度趋势分析
    if len(sorted_months) >= 2:
        recent_months = sorted_months[-2:]
        if len(recent_months) == 2:
            prev_month_count = recent_months[0][1]
            current_month_count = recent_months[1][1]
            if current_month_count > prev_month_count:
                report.append("6. **活跃度趋势**: 最近月份PR数量呈上升趋势，社区活跃度增加")
            elif current_month_count < prev_month_count:
                report.append("6. **活跃度趋势**: 最近月份PR数量有所下降，可能进入稳定期")
            else:
                report.append("6. **活跃度趋势**: 最近月份PR数量保持稳定")

    report.append("")
    report.append("---")
    report.append("*报告生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "*")

    return '\n'.join(report)

def main():
    """主函数 - 需要添加实际的PR数据"""
    print("Kubespray PR分析脚本")
    print("请注意：需要添加实际的PR数据才能运行分析")

    # 这里需要添加从GitHub API获取的PR数据
    # all_prs = [...]  # 实际的PR数据

    # analysis_result = analyze_prs(all_prs)
    # report = generate_report(analysis_result)
    # print(report)

if __name__ == "__main__":
    main()