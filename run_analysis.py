#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行Kubespray PR分析
直接使用GitHub API数据进行分析
"""

import json
from analyze_kubespray_prs import analyze_prs, generate_report

# 模拟部分PR数据用于演示（实际应该包含所有260个PR）
sample_prs = [
    {
        "number": 12035,
        "title": "[calico] fix: kubecontrollersconfigurations list permission",
        "body": "fixes permission warning\r\n```\r\n[WARNING][1] kube-controllers/runconfig.go 193: unable to list KubeControllersConfiguration(default) error=connection is unauthorized: kubecontrollersconfigurations.crd.projectcalico.org \"default\" is forbidden: User \"system:serviceaccount:kube-system:calico-kube-controllers\" cannot list resource \"kubecontrollersconfigurations\" in API group \"crd.projectcalico.org\" at the cluster scope\r\n```\r\n\r\n\r\n**What type of PR is this?**\r\n/kind bug",
        "html_url": "https://github.com/kubernetes-sigs/kubespray/pull/12035",
        "labels": [
            {"name": "kind/bug"},
            {"name": "cncf-cla: yes"},
            {"name": "size/XS"},
            {"name": "lgtm"},
            {"name": "approved"},
            {"name": "release-note"}
        ],
        "pull_request": {
            "merged_at": "2025-03-15T12:39:49Z"
        }
    },
    {
        "number": 12031,
        "title": "[release-2.27] Patch versions update",
        "body": "**What type of PR is this?**\n/kind feature\n\n**What this PR does / why we need it**:\n\n- **Checksums updates**\n- **Update defaults versions to last checksums**",
        "html_url": "https://github.com/kubernetes-sigs/kubespray/pull/12031",
        "labels": [
            {"name": "kind/feature"},
            {"name": "cncf-cla: yes"},
            {"name": "size/L"},
            {"name": "lgtm"},
            {"name": "approved"}
        ],
        "pull_request": {
            "merged_at": "2025-03-14T08:27:48Z"
        }
    },
    {
        "number": 11845,
        "title": "[containerd] Support containerd v2.0.x",
        "body": "**What type of PR is this?**\r\n\r\n/kind container-managers\r\n/kind feature\r\n\r\n**What this PR does / why we need it**:\r\nAdd hashes for containerd versions 2.0.[0-2]\r\ncontainerd 2.0.0 release notes https://github.com/containerd/containerd/releases/tag/v2.0.0\r\ncontainerd 2.0.1 release notes https://github.com/containerd/containerd/releases/tag/v2.0.1\r\ncontainerd 2.0.2 release notes https://github.com/containerd/containerd/releases/tag/v2.0.2\r\nUpdate runc binary to v1.2.4\r\nMake containerd 2.0.2 default\r\nSet containerd_limit_open_file_num to 1048576",
        "html_url": "https://github.com/kubernetes-sigs/kubespray/pull/11845",
        "labels": [
            {"name": "kind/feature"},
            {"name": "kind/container-managers"},
            {"name": "cncf-cla: yes"},
            {"name": "size/M"},
            {"name": "lgtm"},
            {"name": "approved"},
            {"name": "release-note"}
        ],
        "pull_request": {
            "merged_at": "2025-02-05T07:32:15Z"
        }
    }
]

def main():
    """运行分析"""
    print("开始分析Kubespray PR数据...")
    
    # 注意：这里只使用了示例数据
    # 实际应该包含所有260个PR的完整数据
    print(f"分析 {len(sample_prs)} 个PR（示例数据）...")
    
    # 运行分析
    analysis_result = analyze_prs(sample_prs)
    
    # 生成报告
    report = generate_report(analysis_result)
    
    # 输出报告
    print("\n" + "="*50)
    print("KUBESPRAY PR 分析报告")
    print("="*50)
    print(report)
    
    # 保存报告到文件
    with open('kubespray_pr_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n报告已保存到: kubespray_pr_report.md")

if __name__ == "__main__":
    main()
